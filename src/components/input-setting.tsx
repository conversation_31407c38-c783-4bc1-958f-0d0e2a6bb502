import { useCreationStore } from '@/stores/creation'
import { Button } from './ui/button'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from './ui/select'
import { Slider } from './ui/slider'

export const InputSetting = () => {
  const { costomInputSetting } = useCreationStore()

  return (
    <div className="w-full grid grid-cols-2 gap-3 pl-14 2xl:pl-0">
      <div className="relative border rounded-md p-4 bg-muted/50">
        <p className="text-center text-sm font-medium mb-2">图像比例</p>
        <div className="relative flex gap-2 items-center">
          <div className="relative size-32">
            <div
              className="max-w-full max-h-full mx-auto bg-white rounded-md border-2 border-black flex justify-center items-center"
              style={{
                aspectRatio: 1,
              }}
            >
              {costomInputSetting.aspect}
            </div>
          </div>
          <div className="flex-1 p-4">
            <div className="py-4 flex justify-center border-b">
              <Button variant="outline" size="sm" className="rounded-r-none">
                纵向
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-none -ml-[1px]"
              >
                正方形
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                横向
              </Button>
            </div>
            <div className="w-full py-6">
              <Slider
                defaultValue={[5]}
                max={10}
                step={1}
                min={0}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-4 bg-muted/50">
        <p className="text-center text-sm font-medium mb-2">美学设置</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              风格化
            </p>
            <div className="flex-1">
              <Slider
                defaultValue={[100]}
                max={1000}
                step={50}
                min={0}
                className="w-full"
              />
            </div>
          </div>
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              怪异化
            </p>
            <div className="flex-1">
              <Slider
                defaultValue={[0]}
                max={3000}
                step={100}
                min={0}
                className="w-full"
              />
            </div>
          </div>
          <div className="flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              多样化
            </p>
            <div className="flex-1">
              <Slider
                defaultValue={[0]}
                max={100}
                step={10}
                min={0}
                className="w-full"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-4 bg-muted/50">
        <Button
          variant="ghost"
          size="sm"
          className="absolute top-1 right-2 font-light"
        >
          重置
        </Button>
        <p className="text-center text-sm font-medium mb-2">模型</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              模式
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="default" size="sm" className="rounded-r-none">
                标准
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                原始
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              版本
            </p>
            <div className="flex-1 flex justify-end gap-2 items-center">
              <div className="flex-1 flex justify-end">
                <Button variant="outline" size="sm" className="rounded-r-none">
                  标准
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  className="rounded-l-none -ml-[1px]"
                >
                  速写
                </Button>
              </div>
              <Select value="v7">
                <SelectTrigger className="max-w-24">
                  <SelectValue placeholder="选择版本" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>版本</SelectLabel>
                    <SelectItem value="v7">v7</SelectItem>
                    <SelectItem value="niji6">niji6</SelectItem>
                    <SelectItem value="v6.1">v6.1</SelectItem>
                    <SelectItem value="v6">v6</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
      <div className="relative border rounded-md p-4 bg-muted/50">
        <p className="text-center text-sm font-medium mb-2">更多选项</p>
        <div className="relative">
          <div className="border-b flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              速度
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="outline" size="sm" className="rounded-r-none">
                慢速
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-none -ml-[1px]"
              >
                快速
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                极速
              </Button>
            </div>
          </div>
          {/* <div className="flex gap-2 items-center py-4 border-b">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              隐身
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="default" size="sm" className="rounded-r-none">
                开
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                关
              </Button>
            </div>
          </div> */}
          <div className="flex gap-2 items-center py-4 border-b">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              视频清晰度
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="default" size="sm" className="rounded-r-none">
                SD
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                HD
              </Button>
            </div>
          </div>
          <div className="flex gap-2 items-center py-4">
            <p className="text-sm min-w-44 font-medium text-muted-foreground">
              视频个数
            </p>
            <div className="flex-1 flex justify-end">
              <Button variant="outline" size="sm" className="rounded-r-none">
                1
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="rounded-none -ml-[1px]"
              >
                2
              </Button>
              <Button
                variant="default"
                size="sm"
                className="rounded-l-none -ml-[1px]"
              >
                4
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
