import { Feather } from 'lucide-react'

export const isProduction = process.env.NODE_ENV === 'production'
// export const API_URL = `http://172.16.105.191:8100/api`
// export const API_URL = `https://ai-demo-api.elephantailab.com/api`
export const API_URL = isProduction ? `/api` : `http://172.16.105.191:8100/api`
export const MJ_API_URL = `https://api.fotomore.com/api/creations/v1`
// export const MJ_API_URL = `https://api-v2-pre.vcg.com/api/creations/v1`
// export const MJ_API_URL = `http://*************/api/creations/v1`
export const navLists = [
  {
    name: '创作',
    icon: Feather,
    path: '/',
  },
  // {
  //   name: "高级编辑",
  //   icon: Sparkles,
  //   path: "/advanced-edit",
  // },
]

export type ImageInfoType = {
  width: number
  height: number
  base64: string
  hasTransparentPart: boolean
}

export const imageProportions = [
  {
    id: 'ip1',
    name: '1:1',
    value: 1,
    width: 1024,
    height: 1024,
  },
  {
    id: 'ip2',
    name: '16:9',
    value: 16 / 9,
    width: 1024,
    height: 576,
  },
  {
    id: 'ip3',
    name: '9:16',
    value: 9 / 16,
    width: 576,
    height: 1024,
  },
  {
    id: 'ip4',
    name: '2:3',
    value: 2 / 3,
    width: 680,
    height: 1020,
  },
  {
    id: 'ip5',
    name: '3:2',
    value: 3 / 2,
    width: 1020,
    height: 680,
  },
  {
    id: 'ip6',
    name: '3:4',
    value: 3 / 4,
    width: 768,
    height: 1024,
  },
  {
    id: 'ip7',
    name: '4:3',
    value: 4 / 3,
    width: 1024,
    height: 768,
  },
  {
    id: 'ip8',
    name: '3:1',
    value: 3 / 1,
    width: 1536,
    height: 512,
  },
]

export const socialMediaProportions = [
  {
    id: 'smp1',
    name: '微信公众号封面',
    value: 900 / 383,
    width: 900,
    height: 383,
  },
  {
    id: 'smp2',
    name: '微信朋友圈',
    value: 1 / 1,
    width: 1080,
    height: 1080,
  },
  {
    id: 'smp3',
    name: '微信视频号横版封面',
    value: 16 / 9,
    width: 1080,
    height: 608,
  },
  {
    id: 'smp4',
    name: '微信视频号竖版封面',
    value: 108 / 126,
    width: 1080,
    height: 1260,
  },
  {
    id: 'smp5',
    name: '微信视频号直播封面',
    value: 81 / 144,
    width: 810,
    height: 1440,
  },
  {
    id: 'smp6',
    name: '微信图文',
    value: 3 / 4,
    width: 1242,
    height: 1660,
  },
  {
    id: 'smp7',
    name: '微博主页背景图',
    value: 98 / 30,
    width: 980,
    height: 300,
  },
  {
    id: 'smp8',
    name: '小红书主页背景图',
    value: 5 / 4,
    width: 1000,
    height: 800,
  },
  {
    id: 'smp9',
    name: '小红书图文（竖版）',
    value: 1080 / 1440,
    width: 1080,
    height: 1440,
  },
  {
    id: 'smp10',
    name: '小红书图文（横版）',
    value: 1440 / 1080,
    width: 1440,
    height: 1080,
  },
  {
    id: 'smp11',
    name: '抖音主页背景图',
    value: 1125 / 633,
    width: 1125,
    height: 633,
  },
  {
    id: 'smp12',
    name: '抖音图文（横版）',
    value: 144 / 108,
    width: 1440,
    height: 1080,
  },
  {
    id: 'smp13',
    name: '抖音图文（竖版）',
    value: 108 / 144,
    width: 1080,
    height: 1440,
  },
  {
    id: 'smp14',
    name: '抖音视频封面（竖版）',
    value: 1080 / 1920,
    width: 1080,
    height: 1920,
  },
  {
    id: 'smp15',
    name: '抖音视频封面（横版）',
    value: 1920 / 1080,
    width: 1920,
    height: 1080,
  },
]

export const INPUT_SETTING_DEFAULT = {
  aspect: 1,
  stylize: 100,
  weird: 0,
  chaos: 0,
  raw: false,
  version: '7',
  draft: false, // 草图模式，只有在 version 为 7 的时候才能开启
  fast: true,
  relax: false,
  turbo: false,
  videoType: 0, // 0: 480p, 1: 720p
  batchsize: 4, // 1, 2, 4
}

export const videoTypes = [
  {
    id: '0',
    name: '480p',
    value: 0,
  },
  {
    id: '1',
    name: '720p',
    value: 1,
  },
]

// 常见尺寸比例
export const commonAspectRatios = [
  {
    id: '1:2',
    name: '1:2',
    value: 1 / 2,
  },
  {
    id: '9:16',
    name: '9:16',
    value: 9 / 16,
  },
  {
    id: '2:3',
    name: '2:3',
    value: 2 / 3,
  },
  {
    id: '3:4',
    name: '3:4',
    value: 3 / 4,
  },
  {
    id: '5:6',
    name: '5:6',
    value: 5 / 6,
  },
  {
    id: '1:1',
    name: '1:1',
    value: 1,
  },
  {
    id: '6:5',
    name: '6:5',
    value: 6 / 5,
  },
  {
    id: '4:3',
    name: '4:3',
    value: 4 / 3,
  },
  {
    id: '3:2',
    name: '3:2',
    value: 3 / 2,
  },
  {
    id: '16:9',
    name: '16:9',
    value: 16 / 9,
  },
  {
    id: '2:1',
    name: '2:1',
    value: 2,
  },
]
