import { RefreshCcw } from 'lucide-react'
import { useState } from 'react'
import { useNavigate } from 'react-router'
import ChangePasswordDialog from '@/components/change-password-dialog'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardAction,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { useCommonStore } from '@/stores/common'

export function HydrateFallback() {
  return <div>Loading...</div>
}

export default function AccountPage() {
  const [changePasswordOpen, setChangePasswordOpen] = useState(false)
  const { user, credit, getCredits, creditLoading, userLoading } =
    useCommonStore()
  const navigate = useNavigate()

  return (
    <div className="flex h-full overflow-y-auto flex-col overscroll-none bg-muted/40">
      <div className="p-4 max-w-[768px] w-full min-h-full mx-auto gap-4 flex flex-col justify-center">
        {user ? (
          <>
            <Card className="w-full">
              <CardHeader>
                <CardTitle>个人资料</CardTitle>
                <CardAction>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setChangePasswordOpen(true)}
                  >
                    修改密码
                  </Button>
                </CardAction>
              </CardHeader>
              <CardContent className="flex gap-3 items-center">
                <Avatar className="size-14">
                  <AvatarImage
                    src={undefined}
                    alt={user.nick_name || user.name}
                  />
                  <AvatarFallback>
                    {(user.nick_name || user.name).slice(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="">
                  <p className="font-medium text-xl">
                    {user.nick_name || user.name}
                  </p>
                  {user.company && <p>{user.company}</p>}
                </div>
              </CardContent>
            </Card>
            <Card className="w-full">
              <CardHeader>
                <CardTitle className="tabular-nums">套餐使用详情</CardTitle>
                <CardAction>
                  <Button
                    size="icon"
                    variant="outline"
                    onClick={getCredits}
                    title="刷新"
                    className="size-7"
                  >
                    <RefreshCcw
                      className={creditLoading ? 'animate-spin' : ''}
                    />
                  </Button>
                </CardAction>
              </CardHeader>
              <CardContent className="space-y-2">
                {credit ? (
                  <>
                    <div className="text-sm flex justify-between">
                      <p className="text-muted-foreground">
                        {credit.package_type === 'company' ? '总' : '个人'}
                        套餐金额：
                      </p>
                      <p className="ml-auto text-foreground">
                        {credit.recharge_amount} 元
                      </p>
                    </div>
                    <div className="text-sm flex justify-between">
                      <p className="text-muted-foreground">
                        {credit.package_type === 'company' ? '总' : '个人'}
                        套餐余额：
                      </p>
                      <p className="ml-auto text-foreground">
                        {credit.balance} 元
                      </p>
                    </div>
                    {credit.end_at && (
                      <div className="text-sm flex justify-between">
                        <p className="text-muted-foreground">套餐截止时间：</p>
                        <p className="ml-auto text-foreground">
                          {credit.end_at}
                        </p>
                      </div>
                    )}
                    {credit.time && (
                      <div className="text-sm flex justify-between">
                        <p className="text-muted-foreground">更新时间：</p>
                        <p className="ml-auto text-foreground">{credit.time}</p>
                      </div>
                    )}
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground text-center">
                    您的账号没有套餐
                  </p>
                )}
              </CardContent>
            </Card>
          </>
        ) : (
          <div className="">
            <Button className="" onClick={() => navigate('/login')}>
              登录
            </Button>
          </div>
        )}
      </div>
      <ChangePasswordDialog
        open={changePasswordOpen}
        onOpenChange={setChangePasswordOpen}
      />
    </div>
  )
}
